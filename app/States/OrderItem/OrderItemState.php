<?php

namespace App\States\OrderItem;

use App\Models\MarketplaceOrder;
use App\States\Transitions\{
    ContentAwaitingPublisherApprovalTransition,
    PublicationDeliveredTransition,
    RequirementAwaitingPublisherApprovalTransition
};
use Spa<PERSON>\ModelStates\State;
use <PERSON><PERSON>\ModelStates\StateConfig;

/*********************************************************************
 * ORDER ITEM STATE BASE CLASS
 **********************************************************************
 *
 * This is the base state class for order items. It defines the common
 * interface and behavior for all order item states, including state
 * transitions and helper methods.
 *
 *********************************************************************/
abstract class OrderItemState extends State
{
    /*********************************************************************
     * GET STATE LABEL
     **********************************************************************
     *
     * Returns the human-readable label for this state.
     * Must be implemented by concrete state classes.
     *
     * @return string The display label for this state
     *
     *********************************************************************/
    abstract public static function label(): string;

    /*********************************************************************
     * HANDLE STATE TRANSITION
     **********************************************************************
     *
     * Handles the state transition logic. Must be implemented by
     * concrete state classes.
     *
     * @param MarketplaceOrder $model The order item model instance
     * @param string $from The previous state
     * @param string $to The new state
     * @return void
     *
     *********************************************************************/
    abstract public static function handle(MarketplaceOrder $model, string $from, string $to): void;

    /*********************************************************************
     * CONFIGURE STATE TRANSITIONS
     **********************************************************************
     *
     * Defines the state machine configuration, including all possible
     * state transitions and their conditions.
     *
     * @return StateConfig The state machine configuration
     *
     *********************************************************************/
    public static function config(): StateConfig
    {
        return parent::config()
            ->default(RequirementsPending::class) // Default State

            // -----------------
            // Requirement Phase
            ->allowTransition(RequirementsPending::class, RequirementAwaitingPublisherApproval::class, RequirementAwaitingPublisherApprovalTransition::class) // Need Requirement Approval From Publisher
            ->allowTransition(RequirementAwaitingPublisherApproval::class, OrderItemCancelled::class) // Cancel Order From Publisher
            ->allowTransition(RequirementAwaitingPublisherApproval::class, RequirementRevisionRequested::class) // Need Revision From Advertiser
            ->allowTransition(RequirementRevisionRequested::class, RequirementAwaitingPublisherApproval::class) // Revision Approval From Publisher
            ->allowTransition(RequirementAwaitingPublisherApproval::class, ContentPending::class) // Content Pending From Advertiser And Approved From Publisher

            // -----------------
            // Content Phase

            // if content is provided by advertiser
            ->allowTransition(ContentPending::class, ContentAwaitingPublisherApproval::class, ContentAwaitingPublisherApprovalTransition::class) // Content Awaiting Approval From Publisher
            ->allowTransition(ContentAwaitingPublisherApproval::class, PublicationInProcess::class) // Starting Publishing Process And Approved from Publisher
            ->allowTransition(ContentAwaitingPublisherApproval::class, ContentPending::class) // Content Disapproved By Publisher & Need Revision From Advertiser

            // if Content Is Written By Us
            ->allowTransition(ContentPending::class, ContentAssignedToWriter::class) // Content Assigned To Our Writer
            ->allowTransition(ContentPending::class, ContentAdvertiserReview::class) // Content Review From Advertiser
            ->allowTransition(ContentAssignedToWriter::class, ContentAdvertiserReview::class) // Content Review From Advertiser

            // Review Conditions Transition
            ->allowTransition(ContentAdvertiserReview::class, ContentAwaitingPublisherApproval::class, ContentAwaitingPublisherApprovalTransition::class) // Content  Approved From Advertiser
            ->allowTransition(ContentAwaitingPublisherApproval::class, ContentAdvertiserReview::class) // Content sent back to Advertiser for Review
            ->allowTransition(ContentAwaitingPublisherApproval::class, ContentRevisionRequestedByAdvertiser::class) // Content  Revision Requested From Publisher
            ->allowTransition(ContentAdvertiserReview::class, ContentRevisionRequestedByAdvertiser::class) // Content Revision Requested to Writer From Advertiser
            ->allowTransition(ContentAdvertiserReview::class, ContentAssignedToWriter::class) // Reassign Writer During Advertiser Review

            // Revision Transition
            ->allowTransition(ContentRevisionRequestedByAdvertiser::class, ContentAdvertiserReview::class) // Once writer updates the content, its sent to advertiser for review.   Content Revision Ready From Writer

            // -----------------
            // Publishing Phase

            ->allowTransition(PublicationInProcess::class, PublicationDelivered::class, PublicationDeliveredTransition::class) // Content Published By Publisher And Ready For Review By Advertiser
            ->allowTransition(PublicationDelivered::class, OrderItemCompleted::class) // Order Completed And Approved From Advertiser

            // Revision Transition
            ->allowTransition(PublicationDelivered::class, PublicationRevisionRequestedByAdvertiser::class) // Revision Requested by Advertiser
            ->allowTransition(PublicationRevisionRequestedByAdvertiser::class, PublicationDelivered::class, PublicationDeliveredTransition::class) // Revision From Publisher after revision request

            // Cancellation & Refund
            ->allowTransition(OrderItemCancelled::class, RefundedToWallet::class); // Cancel Order From Publisher
    }

    /*********************************************************************
     * STATE FILTER HELPERS
     **********************************************************************
     *
     * Helper methods to check the current state of an order item.
     *
     *********************************************************************/

    /*********************************************************************
     * CHECK IF ORDER IS COMPLETED
     **********************************************************************
     *
     * Determines if the current state represents a completed order.
     *
     * @return bool True if the order is completed, false otherwise
     *
     *********************************************************************/
    public function isCompleted(): bool
    {
        return static::class === OrderItemCompleted::class;
    }

    /*********************************************************************
     * CHECK IF ORDER IS REFUNDED
     **********************************************************************
     *
     * Determines if the current state represents a refunded order.
     *
     * @return bool True if the order is refunded, false otherwise
     *
     *********************************************************************/
    public function isRefunded(): bool
    {
        return static::class === RefundedToWallet::class;
    }

    /*********************************************************************
     * CHECK IF ORDER IS CANCELLED
     **********************************************************************
     *
     * Determines if the current state represents a cancelled order.
     *
     * @return bool True if the order is cancelled, false otherwise
     *
     *********************************************************************/
    public function isCancelled(): bool
    {
        return in_array(static::class, [
            OrderItemCancelled::class,
            RefundedToWallet::class,
        ], true);
    }

    /*********************************************************************
     * CHECK IF ORDER IS PENDING
     **********************************************************************
     *
     * Determines if the current state represents a pending order.
     *
     * @return bool True if the order is pending, false otherwise
     *
     *********************************************************************/
    public function isPending(): bool
    {
        return ! in_array(static::class, [
            // PublicationDelivered::class,
            OrderItemRefundRequested::class,
            RefundedToWallet::class,
            OrderItemCompleted::class,
            OrderItemCancelled::class,
        ], true);
    }
}
