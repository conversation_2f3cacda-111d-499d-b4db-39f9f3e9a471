<?php

namespace App\Http\Controllers\Publisher\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderItemsResources extends JsonResource
{
    public function toArray($request)
    {

        $topics = $this->website->topics?->pluck('name')->implode(', ') ?? 'N/A';

        return [
            'id' => $this->id,
            'title' => $this->website->website_domain ?? 'No website',
            'topic' => ($topics) ? $topics : 'N/A',
            'article_topic' => $this->requirements->article_topic,
            'niche' => $this->niche ?? 'general',
            'price_paid' => $this->publisher_payment_paid,
            'stateName' => $this->state_name ?? '',
            'stateLabel' => $this->state_label ?? 'Not assigned',
            'status' => $this->status,
            'delivery_date' => $this->estimated_publication_date_formatted,
            'customer_name' => $this->order->user->name,
            'created_at' => Carbon::parse($this->created_at)->format('Y-m-d'),
        ];
    }
}
