<?php

namespace App\Notifications\User;

use App\Models\MarketplaceSingleOrderItem;
use App\Services\NotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\View;

class UserNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        protected string $message,
        protected int $order_id,
        protected array $data = [],
        protected ?string $view = null,
        protected ?array $attachment = null,
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['database', 'broadcast', 'mail'];
    }

    /**
     * Get the array representation of the notification for storage in the database.
     */
    public function toDatabase(object $notifiable): array
    {
        return [
            'title' => $this->data['emailSubject'] ?? 'Content Updated for Order Item #' . $this->order_id,
            'message' => $this->message,
            'order_id' => $this->order_id,
            'data' => $this->data,
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast(object $notifiable): array
    {
        $order_item = MarketplaceSingleOrderItem::find($this->order_id);

        if (!$order_item) {
            $href = '#';
        } else {
            $href = (new NotificationService())->generateNotificationLink($order_item->id, $order_item->order->items, $notifiable);
        }

        return [
            'title' => $this->data['emailSubject'] ?? 'Content Updated for Order Item #' . $this->order_id,
            'message' => $this->message,
            'order_id' => $this->order_id,
            'href' => $href,
            'data' => $this->data,
        ];
    }


    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => $this->data['emailSubject'] ?? 'Content Updated for Order Item #' . $this->order_id,
            'message' => $this->message,
            'order_id' => $this->order_id,
            'data' => $this->data,
        ];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $mail = (new MailMessage())
            ->subject($this->data['emailSubject'] ?? 'Content Updated for Order Item #' . $this->order_id)
            ->greeting('Hello ' . ($this->data['customerName'] ?? $notifiable->name))
            ->line($this->data['statusLabel'] ?? 'Content has been updated for Order Item #' . $this->order_id)
            ->line('Thank you for using our service!');

        if ($this->view && View::exists($this->view)) {
            $mail->markdown($this->view, ['data' => $this->data]);
        }

        // Attach PDF if provided
        if ($this->attachment) {
            $mail->attachData(
                base64_decode($this->attachment['data'], true),
                $this->attachment['name'],
                ['mime' => 'application/pdf'],
            );
        }

        return $mail;
    }
}
