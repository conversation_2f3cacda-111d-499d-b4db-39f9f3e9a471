<?php

namespace App\Models;

use App\Enums\Role;
use App\Models\Traits\GenerateSignedURL;
use App\Models\Traits\TopicTrait;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/*********************************************************************
 * MARKETPLACE ADMIN WEBSITE MODEL
 *********************************************************************
 *
 * This model represents the admin-facing interface of the shared
 * `marketplace_websites` table.
 *
 * - Used exclusively within the admin panel
 * - Includes additional computed attributes and global scope for outreach users
 * - Automatically initializes SEO stats on creation
 * - Implements Spatie activity logging for all fields
 *
 *********************************************************************/

class MarketplaceAdminWebsite extends Model
{
    use GenerateSignedURL;
    use LogsActivity;
    use TopicTrait;

    /**
     * TABLE COLUMNS

     * @property int $id
     * @property string $website_domain
     * @property int $site_language_id
     * @property int $main_category_id
     * @property int $category_global_rank
     * @property string $site_title
     * @property string $site_description
     * @property string $domain_registration_date
     * @property int $favicon_image_id
     * @property int $screenshot_image_id
     * @property int $initial_price_before_negotiation
     * @property int $profit_share_percentage
     * @property int $guest_post_price
     * @property int $link_insert_price
     * @property int $casino_post_price
     * @property int $adult_post_price
     * @property int $finance_post_price
     * @property int $dating_post_price
     * @property int $cbd_post_price
     * @property int $crypto_post_price
     * @property string $site_requirements
     * @property string $example_post_url
     * @property int $article_validity_in_months
     * @property int $turn_around_time_in_days
     * @property int $indexed_article
     * @property string $link_relation
     * @property int $sponsorship_label
     * @property int $homepage_visible
     * @property int $publisher_user_id
     * @property string $contact_email
     * @property bool $active
     * @property string $site_source
     * @property string $internal_note
     * @property string $created_at
     * @property string $updated_at
     *
     * @property-read User $publisher
     * @property-read Outreach $outreach
     * @property-read MarketplaceWebsiteSeoStat $seoStats
     * @property-read MarketplaceWebsiteLanguage $language
     * @property-read MarketplaceWebsiteCategory $category
     * @property-read Collection|MarketplaceSingleOrderItem $orders
     * @property-read Collection|Keyword $keywords
     * @property-read Collection|Topic $topics
     * @property-read Collection|Chat $chats
     * @property-read Collection|MarketplaceWebsiteCountry $country
     * @property-read Collection|MarketplaceWebsiteLanguage $language
     * @property-read Collection|MarketplaceWebsiteCategory $category
     */

    // TABLE
    // -------------------------------------------------------------------------------- //
    protected $table = 'marketplace_websites';



    // GUARDED
    // -------------------------------------------------------------------------------- //
    protected $guarded = [];



    // TIMESTAMPS
    // -------------------------------------------------------------------------------- //
    public $timestamps = true;



    // APPENDS
    // -------------------------------------------------------------------------------- //
    // Computed attributes added to model's JSON output
    protected $appends = ['created_at_formatted', 'updated_at_formatted', 'verified_status', 'article_validity_toggle'];

    // RELATIONS
    // -------------------------------------------------------------------------------- //


    /*********************************************************************
     * SET ARTICLE VALIDITY IN MONTHS
     *********************************************************************
     *
     * Set the article validity in months, defaulting to 36 if null.
     *
     * @param mixed $value
     * @return void
     *
     *********************************************************************/
    public function setArticleValidityInMonthsAttribute($value): void
    {
        $this->attributes['article_validity_in_months'] = $value ?? config('app.article_validity_limit');
    }





    /*********************************************************************
     * GET CREATED AT FORMATTED
     *********************************************************************
     *
     * Get the created at date formatted as 'd M, Y'.
     *
     * @param mixed $value
     * @return void
     *
     *********************************************************************/
    public function getCreatedAtFormattedAttribute()
    {
        return Carbon::parse($this->created_at)->format('d M, Y');
    }




    /*********************************************************************
     * GET ARTICLE VALIDITY TOGGLE
     *********************************************************************
     *
     * Get the article validity toggle attribute.
     *
     * @return bool
     *
     *********************************************************************/
    public function getArticleValidityToggleAttribute()
    {
        return $this->article_validity_in_months === config('app.article_validity_limit');
    }


    /*********************************************************************
     * GET UPDATED AT FORMATTED
     *********************************************************************
     *
     * Get the updated at date formatted as 'd M, Y'.
     *
     * @return string
     *
     *********************************************************************/
    public function getUpdatedAtFormattedAttribute()
    {
        return Carbon::parse($this->updated_at)->format('d M, Y');
    }



    /*********************************************************************
     * IS VERIFIED
     *********************************************************************
     *
     * Determine if the website is considered verified.
     *
     * @return bool
     *
     *********************************************************************/
    public function isVerified(): bool
    {

        return (bool) ($this->publisher_user_id > 0);
    }


    /*********************************************************************
     * GET VERIFIED STATUS
     *********************************************************************
     *
     * Proxy to `isVerified()` for use in frontend templates.
     *
     * @return bool
     *
     *********************************************************************/
    public function getVerifiedStatusAttribute(): bool
    {
        return $this->isVerified();
    }



    /*********************************************************************
     * PUBLISHER
     *********************************************************************
     *
     * Defines relationship to the publisher (User).
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     *
     *********************************************************************/
    public function publisher(): BelongsTo
    {
        return $this->belongsTo(User::class, 'publisher_user_id');
    }



    /*********************************************************************
     * OUTREACH
     *********************************************************************
     *
     * Defines relationship to the Outreach model.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     *
     *********************************************************************/
    public function outreach(): HasOne
    {
        return $this->hasOne(Outreach::class, 'marketplace_website_id');
    }


    /*********************************************************************
     * SEO STATS
     *********************************************************************
     *
     * Defines relationship to the SEO stats.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     *
     *********************************************************************/
    public function seoStats(): HasOne
    {
        return $this->hasOne(MarketplaceWebsiteSeoStat::class, 'marketplace_website_id');
    }


    /*********************************************************************
     * SEO STATS LOGS
     *********************************************************************
     *
     * Defines relationship to the SEO stats logs.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     *
     *********************************************************************/
    public function seoStatsLogs(): HasMany
    {
        return $this->hasMany(MarketplaceWebsiteSeoStatLog::class, 'marketplace_website_id');
    }


    /*********************************************************************
     * LANGUAGE
     *********************************************************************
     *
     * Defines relationship to the website's primary language.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     *
     *********************************************************************/
    public function language(): BelongsTo
    {
        return $this->belongsTo(MarketplaceWebsiteLanguage::class, 'site_language_id');
    }


    /*********************************************************************
     * CATEGORY
     *********************************************************************
     *
     * Defines relationship to the website's main category.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     *
     *********************************************************************/
    public function category(): BelongsTo
    {
        return $this->belongsTo(MarketplaceWebsiteCategory::class, 'main_category_id');
    }


    /*********************************************************************
     * ORDERS
     *********************************************************************
     *
     * Defines one-to-many relationship with order items placed on this website.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     *
     *********************************************************************/
    public function orders(): HasMany
    {
        return $this->hasMany(MarketplaceSingleOrderItem::class, 'marketplace_website_id', 'id');
    }


    public function domainVerified()
    {
        return $this->belongsTo(DomainVerificationCode::class, 'website_domain', 'domain');
    }


    /*********************************************************************
     * TOPICS
     *********************************************************************
     *
     * Defines many-to-many relationship with Topics.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     *
     *********************************************************************/
    public function topics(): BelongsToMany
    {
        return $this->belongsToMany(Topic::class, 'topic_website', 'marketplace_website_id', 'topic_id');
    }

    /*********************************************************************
     * BOOT
     *********************************************************************
     *
     * The model's boot method.
     *
     * - Adds a global scope for Outreach users to restrict access to websites assigned to them
     *   to websites assigned to them
     * - Automatically creates default SEO stats when a website is created
     *
     * @return void
     *
     *********************************************************************/
    protected static function boot(): void
    {
        parent::boot();

        static::addGlobalScope('outreachWebsites', function ($builder): void {
            if (Auth::check() && Auth::user()->role === Role::Outreach->value) {
                $builder->whereHas('outreach', function ($query): void {
                    $query->where('user_id', Auth::id());
                });
            } elseif (Auth::check() && Auth::user()->role === Role::Publisher->value) {
                $builder->where('publisher_user_id', Auth::id());
            }
        });

        // -----------------------
        // Creating Scope
        // -----------------------
        static::creating(function ($model): void {
            if (Auth::check() && Auth::user()->role === Role::Publisher->value) {
                $model->publisher_user_id = Auth::id();
            }
        });

        // -----------------------
        // Created Scope
        // -----------------------
        static::created(function ($model): void {
            // -----------------------
            // Create SEO stats
            // -----------------------
            if (! $model->seoStats) {
                $model->seoStats()->create([
                    'top_traffic_country_id' => MarketplaceWebsiteCountry::first()->id,
                ]);
            }

            // -----------------------
            // Create Outreach
            // -----------------------
            if (Auth::check() && Auth::user()->role === Role::Outreach->value) {
                $model->outreach()->create([
                    'user_id' => Auth::id(),
                    'status' => 'inprogress',
                    'assigned_at' => now(),
                ]);
            }
        });

        // -----------------------
        // Updated Scope
        // -----------------------
        static::updated(function ($model): void {
            // 2. Handle outreach status logic
            if ($model->isVerified() && $model->active) {
                $model->outreach()->update([
                    'status' => 'onboarded',
                    'onboarded_at' => now(),
                ]);
            } else {
                $model->outreach()->update([
                    'status' => 'inprogress',
                ]);
            }
        });
    }




    /*********************************************************************
     * GET ACTIVITY LOG OPTIONS
     *********************************************************************
     *
     * Configure the Spatie activity log options.
     *
     * - Logs all attributes
     * - Only logs when values change
     * - Ignores empty logs
     *
     * @return \Spatie\Activitylog\LogOptions
     *
     *********************************************************************/
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
