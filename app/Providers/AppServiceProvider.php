<?php

namespace App\Providers;

use App\Enums\OrderItemStates;
use App\Enums\Role;
use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;
use Illuminate\Support\ServiceProvider;
use Inertia\Inertia;

/**
 * Application Service Provider
 *
 * Handles application-wide service registration and bootstrapping.
 * Manages Inertia.js shared data, model configurations, and global settings.
 *
 * Key Responsibilities:
 * - Register application services and providers
 * - Configure Inertia.js shared data for frontend
 * - Set up model behaviors and global scopes
 * - Manage view namespaces and authentication data
 */
class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * Called during the service container binding phase.
     * Registers development-only services like Telescope.
     */
    public function register(): void
    {
        // Register Telescope for local development environment
        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }
    }

    /**
     * Bootstrap any application services.
     *
     * Called after all services are registered.
     * Configures global behaviors and Inertia.js shared data.
     */
    public function boot(): void
    {
        // Prevent lazy loading to catch N+1 query issues early
        Model::preventLazyLoading();

        // Add mail namespace for custom email templates
        View::addNamespace('mail', resource_path('views/vendor/mail'));

        // Configure Inertia.js shared data for frontend
        $this->configureInertiaSharedData();
    }

    /**
     * Configure Inertia.js shared data
     *
     * Sets up data that will be available to all Inertia.js pages.
     * Includes authentication, notifications, enums, and user-specific data.
     */
    private function configureInertiaSharedData(): void
    {
        Inertia::share([


            // User notifications with proper formatting and navigation links
            'notifications' => function () {
                $notificationService = app(NotificationService::class);
                return $notificationService->getFormattedNotifications();
            },

            // Order item states enum for frontend use
            'orderItemStates' => collect(OrderItemStates::cases())->mapWithKeys(fn($state) => [
                $state->name => $state->value,
            ]),

            // Super admin access flag for frontend authorization
            'superAdminAccess' => function () {
                $user = Auth::user();
                if (! $user) {
                    return false;
                }
                return $user->isSuperAdmin() || $user->isAdmin();
            },

            // Super admin wallet balance for financial operations
            'superAdminWalletBalance' => function () {
                $user = Auth::user();
                if (! $user) {
                    return 0;
                }

                // Only return balance for admin/super admin users
                if ($user->isSuperAdmin() || $user->isAdmin() || $user->isPublisher()) {
                    return $user->user_balance;
                }

                return 0;
            },

            // Role enums for frontend role management
            'roleEnums' => collect(Role::cases())->mapWithKeys(fn($role) => [
                $role->name => $role->value,
            ]),
        ]);
    }
}
