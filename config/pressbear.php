<?php

/**************************************
 * Globally accessible variables
 * custom config file for this project
 ***************************************/


return [
    'name' => 'pressbear',



    /*
    |--------------------------------------------------------------------------
    | SENSITIVE DATA
    |--------------------------------------------------------------------------
    | Sensitive data stored here and used across the app. Data that's not
    | dependent on enviornment.
    |
    |
    */
    'super_admin_email' => '<EMAIL>', //used for redundancy verifications


    /*
    |--------------------------------------------------------------------------
    | Admin Settings
    |--------------------------------------------------------------------------
    | Used for admin routes prefix
    |
    */
    'admin_prefix' => 'pb-admin',
    'currency' => 'USD',
    'currency_symbol' => '$',


    /*
    |--------------------------------------------------------------------------
    | Code Variable Settings
    |--------------------------------------------------------------------------
    |
    |
    */
    'delete_old_cart_items_days' => 60,
    'signed_url_expiry_days' => 7,
    'order_processing_delay' => 1, // 1 minute
    'content_writing_fee' => 0, // Additional fee for content writing service


    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    |
    |
    */
    // 1 minute
    'oneMinuteCache' => 60,

    // 5 minutes
    'fiveMinuteCache' => 300,

    // 1 hour
    'oneHourCache' => 3600,

    // Instant Cache
    'instantCache' => 10,




    /*
    |--------------------------------------------------------------------------
    | Social Media URLs
    |--------------------------------------------------------------------------
    |
    | URLS for social media
    | Instagram, facebook, linkedin, twitter/x
    | used to display urls at frontend
    |
    */
    'instagram_url' => '#',
    'facebook_url' => '#',
    'linkedin_url' => 'https://www.linkedin.com/company/pressbear/',
    'x_url' => '#',
    'advertiser_guideline_words_limit' => 300,

    /*
    |--------------------------------------------------------------------------
    | Advertiser Settings
    |--------------------------------------------------------------------------
    |
    | Default pagination for advertiser orders and other settings
    |
    */
    'advertiser_default_pagination' => 10,



    /*
    |--------------------------------------------------------------------------
    | Default Pagination
    |--------------------------------------------------------------------------
    |
    | Default pagination for various models
    |
    */
    'default_pagination_10' => 10,
    'default_pagination_25' => 25,
    'default_pagination_50' => 50,
    'default_pagination_100' => 100,



    /*
    |--------------------------------------------------------------------------
    | SEO Queue Settings
    |--------------------------------------------------------------------------
    |
    | Settings for the SEO queue
    */
    'seo_api_url' => 'https://api.sponsix.com/api/get-data',
    'seo_api_token' => 'deltadroplet@pressbear123',

    'seo_queue_tries' => 1, // used for queue retries
    'seo_queue_timeout' => 120, // used for queue timeout
    'seo_queue_name' => 'seoStatsFetcher', // used for queue name
    'seo_queue_expiry_days' => 30, // used for queue expiry
    'seo_fetch_limit' => 100, // used for queue fetch limit



    /* Environment Variables */
    'env' => [
        'default_payment_method' => env('PAYMENT_METHOD', 'stripe'),
    ],
];
