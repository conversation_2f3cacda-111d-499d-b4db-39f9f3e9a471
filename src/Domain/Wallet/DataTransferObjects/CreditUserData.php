<?php

declare(strict_types=1);

namespace Domain\Wallet\DataTransferObjects;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Rule;
use <PERSON><PERSON>\LaravelData\Data;

/*********************************************************************
 * CREDIT USER DATA TRANSFER OBJECT
 *********************************************************************
 *
 * Represents the data structure for crediting a user's wallet.
 * This DTO encapsulates the required data for credit operations including
 * user ID, amount, and reason.
 *
 * Properties:
 * - userId: The ID of the user to credit
 * - amount: The amount to credit
 * - reason: The reason for the credit
 *
 * Validation:
 * - User ID must exist in users table
 * - Amount must be a valid number between 0.01 and 10000
 * - Reason must be a string with a maximum length of 500 characters
 *
 *********************************************************************/
class CreditUserData extends Data
{
    public function __construct(
        #[Rule('integer|exists:users,id')]
        public int $user_id,
        #[Rule('numeric|min:0.01|max:10000')]
        public int $amount,
        #[Rule('string|max:500')]
        public string $reason,
    ) {}
}
