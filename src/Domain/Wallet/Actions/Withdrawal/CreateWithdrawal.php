<?php

namespace Domain\Wallet\Actions\Withdrawal;

use App\Models\User;
use App\Models\WalletWithdrawalRequest;
use Illuminate\Support\Facades\DB;
use App\Notifications\NewWithdrawalRequest;
use Domain\Wallet\Actions\TransferCredit;

class CreateWithdrawal
{

    /*********************************************************************
     * CREATE WITHDRAWAL ACTION
     *********************************************************************
     *
     * Creates a withdrawal from a user's wallet.
     *
     * @param User $user
     * The user to create the withdrawal for
     *
     * @param array $data
     * Withdrawal data including amount and reason
     *
     * @return bool
     * True if the withdrawal was created successfully, false otherwise
     *
     *********************************************************************/
    public function __invoke(User $user, array $data): bool
    {
        try {

            DB::beginTransaction();

            // Create withdrawal request
            $withdrawalRequest = WalletWithdrawalRequest::create([
                'user_id' => $user->id,
                'amount' => $data['amount'],
                'payment_method_id' => $data['payment_method_id'] ?? null,
                'status' => 'pending',
            ]);

            // Transfer balance to super admin wallet
            (new TransferCredit())($user, super_admin_user(), [
                'amount' => $data['amount'],
                'reference' => 'Withdrawal Request',
                'type' => 'withdrawal_request',
                'message' => 'Withdrawal request submitted',
                'withdrawal_request_id' => $withdrawalRequest->id,
            ]);

            DB::commit();

            // Notify super admin
            super_admin_user()->notify(new NewWithdrawalRequest($withdrawalRequest));

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            return false;
        }
    }
}
