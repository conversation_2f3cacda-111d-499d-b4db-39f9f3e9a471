<?php

namespace Domain\Wallet\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TransitionResource extends JsonResource
{
    /*************************************************
     * toArray
    /************************************************/
    public function toArray($request): array
    {
        $debit = $this->type === 'withdraw' ? number_format($this->amount / 100, 2) : 0;
        $credit = $this->type === 'deposit' ? number_format($this->amount / 100, 2) : 0;

        return [
            'id' => $this->id,
            'type' => $this->type,
            'user' => $this->wallet->holder,
            'order_id' => $this->meta['order_id'] ?? 'N/A',
            'amount' => $this->amount,
            'reference' => $this->meta['reference'] ?? 'N/A',
            'status' => $this->confirmed ? 'Approved' : 'Pending',
            'date' => $this->created_at->format('Y-m-d H:i:s'),
            'debit' => $debit,
            'credit' => $credit,
        ];
    }
}
