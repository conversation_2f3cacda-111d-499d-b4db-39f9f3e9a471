<?php

namespace Domain\Wallet\Requests;

use App\Enums\Role;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;

class WithdrawalRequest extends FormRequest
{
    public function rules()
    {
        $user = $this->user();

        $isAdvertiser = $user->role === Role::Advertiser->value;
        $balance = $user->formatted_balance;

        return [
            'amount' => 'required|numeric|min:1|max:' . $balance,
            'payment_method_id' => $isAdvertiser ? 'nullable|integer|exists:wallet_payout_details,id' : 'required|integer|exists:wallet_payout_details,id',
        ];
    }

    public function messages()
    {
        return [
            'amount.required' => 'The amount is required',
            'amount.numeric' => 'The amount must be a number',
            'amount.min' => 'The amount must be greater than 0',
            'amount.max' => 'The amount must be less than or equal to your wallet balance',
            'payment_method_id.required' => 'The payment method is required',
            'payment_method_id.exists' => 'The payment method does not exist',
        ];
    }
}
