<div id="app-body" class="flex flex-col min-h-screen pt-14 mx-2 sm:mx-16">


    <main>

        <div class="relative isolate overflow-hidden  bg-white rounded-xl border">
            <!-- Secondary navigation -->
            <header class="pb-4 pt-6 sm:pb-6 bg-white">
                <div class="mx-auto flex  flex-wrap items-center gap-6 px-4 sm:flex-nowrap sm:px-6 lg:px-8">
                    <h1 class="text-base font-semibold leading-7 text-zinc-700">Advertiser Dashboard</h1>
                    {{-- <div
                        class="order-last flex w-full gap-x-8 text-sm font-semibold leading-6 sm:order-none sm:w-auto sm:border-l sm:border-gray-200 sm:pl-6 sm:leading-7">
                        <a href="#" class="text-orange-600">Today</a>
                        <a href="#" class="text-zinc-600">Last 7 days</a>
                        <a href="#" class="text-zinc-600">Last 30 days</a>
                    </div> --}}
                    <a href="{{ route('marketplace') }}"
                        class="ml-auto flex items-center gap-x-1 rounded-md bg-orange-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-orange-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-orange-600">
                        <svg class="-ml-1.5 h-5 w-5" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path
                                d="M10.75 6.75a.75.75 0 00-1.5 0v2.5h-2.5a.75.75 0 000 1.5h2.5v2.5a.75.75 0 001.5 0v-2.5h2.5a.75.75 0 000-1.5h-2.5v-2.5z" />
                        </svg>
                        New Order
                    </a>
                </div>
            </header>
            <!-- Stats -->
            <div class="border-b border-b-gray-900/10 lg:border-t rounded lg:border-t-gray-900/5">
                <dl class="mx-auto grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 lg:px-2 xl:px-0">
                    <div
                        class="flex items-baseline flex-wrap justify-between gap-y-2 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 lg:border-t-0 xl:px-8">
                        <dt class="text-sm font-medium leading-6 text-gray-500">Total Spent</dt>
                        <dd class="w-full flex-none text-2xl font-medium leading-10 tracking-tight text-gray-900">
                            ${{ number_format($this->totalSpent, 2) }}</dd>
                    </div>
                    <div
                        class="flex items-baseline flex-wrap justify-between gap-y-2 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 lg:border-t-0 xl:px-8 sm:border-l">
                        <dt class="text-sm font-medium leading-6 text-gray-500">Refunds</dt>
                        <dd class="w-full flex-none text-2xl font-medium leading-10 tracking-tight text-gray-900">
                            ${{ number_format($this->totalRefunds, 2) }}</dd>
                    </div>
                    <div
                        class="flex items-baseline flex-wrap justify-between gap-y-2 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 lg:border-t-0 xl:px-8 lg:border-l">
                        <dt class="text-sm font-medium leading-6 text-gray-500">Wallet Balance</dt>
                        <dd class="w-full flex-none text-2xl font-medium leading-10 tracking-tight text-gray-900">
                            ${{ $this->walletBalance }}</dd>
                    </div>
                </dl>
            </div>

            <div class="absolute left-0 top-full -z-10 mt-96 origin-top-left translate-y-40 -rotate-90 transform-gpu opacity-20 blur-3xl sm:left-1/2 sm:-ml-96 sm:-mt-10 sm:translate-y-0 sm:rotate-0 sm:transform-gpu sm:opacity-50"
                aria-hidden="true">
                <div class="aspect-[1154/678] w-[72.125rem] bg-gradient-to-br from-[#FF80B5] to-[#9089FC]"
                    style="clip-path: polygon(100% 38.5%, 82.6% 100%, 60.2% 37.7%, 52.4% 32.1%, 47.5% 41.8%, 45.2% 65.6%, 27.5% 23.4%, 0.1% 35.3%, 17.9% 0%, 27.7% 23.4%, 76.2% 2.5%, 74.2% 56%, 100% 38.5%)">
                </div>
            </div>
        </div>



        {{-- Order Stats --}}
        <div class="relative isolate overflow-hidden mt-10">

            <!-- Stats -->
            <div class="border-b border-b-gray-900/10 lg:border-t lg:border-t-gray-900/5 bg-white rounded-xl border">
                <dl class="mx-auto grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 lg:px-2 xl:px-0">
                    <div
                        class="flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 lg:border-t-0 xl:px-8">
                        <dt class="text-sm font-medium leading-6 text-zinc-800 pb-2 font-semibold tracking-light">Orders
                            in Progress</dt>
                        {{-- <dd class="text-xs font-medium text-gray-700">+4.75%</dd> --}}
                        <dd class="w-full flex-none text-3xl font-medium leading-10 tracking-tight text-emerald-900">{{
                            $this->inProgress }}
                        </dd>
                    </div>
                    <div
                        class="flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 lg:border-t-0 xl:px-8 sm:border-l">
                        <dt class="text-sm font-medium leading-6 text-zinc-800 pb-2 font-semibold tracking-light">Late
                            Orders</dt>
                        <dd class="w-full flex-none text-3xl font-medium leading-10 tracking-tight text-rose-900">{{
                            $this->late }}
                        </dd>
                    </div>
                    <div
                        class="flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 lg:border-t-0 xl:px-8 lg:border-l">
                        <dt class="text-sm font-medium leading-6 text-zinc-800 pb-2 font-semibold tracking-light">
                            Orders Needing Attention</dt>
                        <dd class="w-full flex-none text-3xl font-medium leading-10 tracking-tight text-emerald-900">
                            {{ $this->needAttention }}
                        </dd>
                    </div>
                    <div
                        class="flex items-baseline flex-wrap justify-between gap-y-1 gap-x-4 border-t border-gray-900/5 px-4 py-10 sm:px-6 lg:border-t-0 xl:px-8 sm:border-l">
                        <dt class="text-sm font-medium leading-6 text-zinc-800 pb-2 font-semibold tracking-light">
                            Completed Orders</dt>
                        <dd class="w-full flex-none text-3xl font-medium leading-10 tracking-tight text-blue-900">
                            {{ $this->completed }}
                        </dd>
                    </div>
                </dl>
            </div>

            <div class="absolute left-0 top-full -z-10 mt-96 origin-top-left translate-y-40 -rotate-90 transform-gpu opacity-20 blur-3xl sm:left-1/2 sm:-ml-96 sm:-mt-10 sm:translate-y-0 sm:rotate-0 sm:transform-gpu sm:opacity-50"
                aria-hidden="true">
                <div class="aspect-[1154/678] w-[72.125rem] bg-gradient-to-br from-[#FF80B5] to-[#9089FC]"
                    style="clip-path: polygon(100% 38.5%, 82.6% 100%, 60.2% 37.7%, 52.4% 32.1%, 47.5% 41.8%, 45.2% 65.6%, 27.5% 23.4%, 0.1% 35.3%, 17.9% 0%, 27.7% 23.4%, 76.2% 2.5%, 74.2% 56%, 100% 38.5%)">
                </div>
            </div>
        </div>





        <!-- Transactions Table Section -->
        <div class=" mt-2 py-10 ">
            <!-- Card -->
            <div class="flex flex-col">
                <div class="-m-1.5 overflow-x-auto">
                    <div class="p-1.5 min-w-full inline-block align-middle">

                        <div
                            class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden dark:bg-slate-900 dark:border-gray-700">
                            <h2
                                class="mt-4 px-6 py-4 mx-auto max-w-2xl text-base font-semibold leading-6 text-zinc-800 lg:mx-0 lg:max-w-none">
                                Recent Orders</h2>
                            <!-- Header -->
                            {{-- <div
                                class="px-6 py-4 grid gap-3 md:flex md:justify-between md:items-center border-b border-gray-200 dark:border-gray-700">

                                <!-- Input -->
                                <div class="sm:col-span-1">
                                    <label for="hs-as-table-product-review-search" class="sr-only">Search</label>
                                    <div class="relative">
                                        <input type="text" id="hs-as-table-product-review-search"
                                            name="hs-as-table-product-review-search" wire:model.live="search"
                                            class="py-2 px-3 pl-11 block w-full border-gray-200 shadow-sm rounded-md text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 dark:bg-slate-900 dark:border-gray-700 dark:text-gray-400"
                                            placeholder="Search orders or customers">
                                        <div
                                            class="absolute inset-y-0 left-0 flex items-center pointer-events-none pl-4">
                                            <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg"
                                                width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                <path
                                                    d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                                <!-- End Input -->
                            </div> --}}
                            <!-- End Header -->

                            <!-- Table -->
                            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-slate-800">
                                    <tr>


                                        <th scope="col" class="pl-6 py-3 text-left">
                                            <div class="flex items-center gap-x-2">
                                                <span
                                                    class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                                                    Order
                                                </span>
                                            </div>
                                        </th>

                                        <th scope="col" class="px-6 py-3 text-left">
                                            <div class="flex items-center gap-x-2">
                                                <span
                                                    class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                                                    Date
                                                </span>
                                            </div>
                                        </th>

                                        <th scope="col" class="px-6 py-3 text-left">
                                            <div class="flex items-center gap-x-2">
                                                <span
                                                    class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                                                    Customer
                                                </span>
                                            </div>
                                        </th>

                                        <th scope="col" class="px-6 py-3 text-left">
                                            <div class="flex items-center gap-x-2">
                                                <span
                                                    class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                                                    Order Status
                                                </span>
                                            </div>
                                        </th>

                                        <th scope="col" class="px-6 py-3 text-left">
                                            <div class="flex items-center gap-x-2">
                                                <span
                                                    class="text-xs font-semibold uppercase tracking-wide text-gray-800 dark:text-gray-200">
                                                    Payment Method
                                                </span>
                                            </div>
                                        </th>
                                    </tr>
                                </thead>

                                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                                    @forelse($this->orders as $order)
                                    <tr>
                                        <td class="h-px w-px whitespace-nowrap">
                                            <div class="pl-6 py-2">
                                                <a class="text-sm text-blue-600 decoration-2 hover:underline dark:text-blue-500"
                                                    href="{{ route('advertiser.order-details', $order->id) }}">{{
                                                    $order->id }}</a>
                                            </div>
                                        </td>
                                        <td class="h-px w-px whitespace-nowrap">
                                            <div class="px-6 py-2">
                                                <span class="text-sm text-gray-600 dark:text-gray-400">{{
                                                    $order->created_at->format('M d, Y, h:i A') }}</span>
                                            </div>
                                        </td>
                                        <td class="h-px w-px whitespace-nowrap">
                                            <div class="px-6 py-2">
                                                <span class="text-sm text-gray-600 dark:text-gray-400">{{
                                                    $order->user->name }}</span>
                                            </div>
                                        </td>
                                        <td class="h-px w-px whitespace-nowrap">
                                            <div class="px-6 py-2">
                                                <span
                                                    class="inline-flex items-center gap-1.5 py-0.5 px-2 rounded-full text-xs font-medium {{ $order->status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' }}">
                                                    {{ ucfirst($order->status) }}
                                                </span>
                                            </div>
                                        </td>
                                        <td class="h-px w-px whitespace-nowrap">
                                            <div class="px-6 py-2">
                                                <span class="text-sm text-gray-600 dark:text-gray-400">{{
                                                    $order->payment_method }}</span>
                                            </div>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                                            No orders found
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                            <!-- End Table -->

                            <!-- Footer -->
                            <div class="px-6 py-4 w-full border-t border-gray-200 dark:border-gray-700">
                                {{ $this->orders->links() }}
                            </div>
                            <!-- End Footer -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- End Card -->
        </div>
        <!-- End Table Section -->



    </main>


</div>