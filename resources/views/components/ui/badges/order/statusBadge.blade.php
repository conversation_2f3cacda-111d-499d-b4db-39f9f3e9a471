@props([
'item' => null,
])

@php
$stateLabel = $item->state_label ?? 'Pending';
$color = $item->badge_color ?? 'orange';
$icon = $item->badge_icon ?? 'triangle-alert';
@endphp

<span class="inline-flex items-center justify-center gap-1 sm:gap-1.5
         py-0.5 px-2 sm:py-1 sm:px-3
         rounded-lg text-xs sm:text-sm font-medium
         bg-{{ $color }}-100 text-{{ $color }}-800
         dark:bg-{{ $color }}-900 dark:text-{{ $color }}-200
         min-w-0 max-w-full">

  @if($icon)
  @component("components.icons.lucide.{$icon}", ['class' => 'w-3 h-3 sm:w-4 sm:h-4 stroke-2 flex-shrink-0'])
  @endcomponent
  @endif

  <span class="truncate max-w-[5rem] sm:max-w-[8rem] md:max-w-none">
    {{ $stateLabel }}
  </span>
</span>