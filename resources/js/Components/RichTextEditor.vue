<template>
  <div class="rich-text-editor border border-[#6b7280] rounded-lg overflow-hidden" :class="{ 'border-red-500': hasError }">
    <!-- Toolbar -->
    <div v-if="editor" class="border-b border-gray-200 bg-gray-50 p-2">
      <div class="flex flex-wrap items-center gap-1">
        <!-- Bold -->
        <button
          type="button"
          @click="editor.chain().focus().toggleBold().run()"
          :class="{ 'bg-gray-200': editor.isActive('bold') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Bold (Ctrl+B)"
        >
          <Bold class="w-4 h-4" />
        </button>

        <!-- Italic -->
        <button
          type="button"
          @click="editor.chain().focus().toggleItalic().run()"
          :class="{ 'bg-gray-200': editor.isActive('italic') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Italic (Ctrl+I)"
        >
          <Italic class="w-4 h-4" />

        </button>

        <!-- Underline -->
        <button
          type="button"
          @click="editor.chain().focus().toggleUnderline().run()"
          :class="{ 'bg-gray-200': editor.isActive('underline') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Underline (Ctrl+U)"
        >
          <UnderlineIcon class="w-4 h-4" />
        </button>

        <!-- Separator -->
        <div class="w-px h-6 bg-gray-300 mx-1"></div>

        <!-- Bullet List -->
        <button
          type="button"
          @click="editor.chain().focus().toggleBulletList().run()"
          :class="{ 'bg-gray-200': editor.isActive('bulletList') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Bullet List"
        >
          <List class="w-4 h-4" />
        </button>

        <!-- Numbered List -->
        <button
          type="button"
          @click="editor.chain().focus().toggleOrderedList().run()"
          :class="{ 'bg-gray-200': editor.isActive('orderedList') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Numbered List"
        >
          <ListOrdered class="w-4 h-4" />
        </button>

        <!-- Separator -->
        <div class="w-px h-6 bg-gray-300 mx-1"></div>

        <!-- Link -->
        <button
          type="button"
          @click="toggleLink"
          :class="{ 'bg-gray-200': editor.isActive('link') }"
          class="p-2 rounded hover:bg-gray-200 transition-colors"
          title="Add Link"
        >
          <LinkIcon class="w-4 h-4" />
        </button>

        <!-- Separator -->
        <div class="w-px h-6 bg-gray-300 mx-1"></div>

        <!-- Undo -->
        <button
          type="button"
          @click="editor.chain().focus().undo().run()"
          :disabled="!editor.can().undo()"
          class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          title="Undo (Ctrl+Z)"
        >
          <Undo class="w-4 h-4" />
        </button>

        <!-- Redo -->
        <button
          type="button"
          @click="editor.chain().focus().redo().run()"
          :disabled="!editor.can().redo()"
          class="p-2 rounded hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          title="Redo (Ctrl+Y)"
        >
          <Redo class="w-4 h-4" />
        </button>
      </div>
    </div>

    <!-- Editor Content -->
    <div
      ref="editorElement"
      class="min-h-[150px] p-1 text-sm/6 text-gray-700 focus-within:ring-0 overflow-y-auto max-h-[200px]"
    ></div>

    <!-- Error Message -->
    <div v-if="hasError && errorMessage" class="text-red-500 text-sm mt-1 px-4 pb-2">
      {{ errorMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { Editor } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Placeholder from '@tiptap/extension-placeholder'
import Underline from '@tiptap/extension-underline'
import Link from '@tiptap/extension-link'
import { Bold, Italic, Underline as UnderlineIcon, List, ListOrdered, Link as LinkIcon, Undo, Redo } from 'lucide-vue-next'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Start typing...'
  },
  hasError: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

const editorElement = ref(null)
const editor = ref(null)

onMounted(async () => {
  await nextTick()
  
  editor.value = new Editor({
    element: editorElement.value,
    extensions: [
      StarterKit,
      Underline,
      Link.configure({
        openOnClick: false,
      }),
      Placeholder.configure({
        placeholder: props.placeholder,
      }),
    ],
    content: props.modelValue,
    editable: !props.disabled,
    onUpdate: ({ editor }) => {
      emit('update:modelValue', editor.getHTML())
    },
  })
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue, false)
  }
})

// Watch for disabled state changes
watch(() => props.disabled, (newDisabled) => {
  if (editor.value) {
    editor.value.setEditable(!newDisabled)
  }
})

// Link functionality
const toggleLink = () => {
  if (editor.value.isActive('link')) {
    editor.value.chain().focus().unsetLink().run()
  } else {
    const url = window.prompt('Enter URL:')
    if (url) {
      editor.value.chain().focus().setLink({ href: url }).run()
    }
  }
}
</script>

<style scoped>
/* Basic editor styling */
:deep(.ProseMirror) {
  outline: none;
  min-height: 120px;
  padding: 0.5rem;
}

/* Placeholder styling */
:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Basic list styling */
:deep(.ProseMirror ul) {
  list-style-type: disc;
  margin-left: 1.5rem;
}

:deep(.ProseMirror ol) {
  list-style-type: decimal;
  margin-left: 1.5rem;
}

:deep(.ProseMirror li) {
  margin: 0.25rem 0;
}
</style>
