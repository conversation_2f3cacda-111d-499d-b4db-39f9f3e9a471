<template>
    <div>
        <div class="lg:flex lg:items-center lg:justify-between border-b pb-8 mb-8">
            <div class="min-w-0 flex-1">
                <h2 class="mt-2 text-2xl/7 font-bold text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
                    My Wallet
                </h2>
            </div>

            <Link v-if="parseFloat(balance.replace(/,/g, '')) > 0" :href="route('wallet.withdraw')" class="btn-indigo">
            <CircleArrowOutUpRight class="h-5 w-5" />
            <span>Transfer to Bank Account</span>
            </Link>
        </div>
        <div class="border-b pb-8 mb-8">
            <div class="lg:flex lg:items-center lg:justify-between mt-5">
                <div class="min-w-0 flex flex-row flex-wrap">
                    <Wallet class="w-32 h-32" />
                    <div class="flex flex-col items-start gap-1 ms-2 mt-2">
                        <span class="text-2xl font-bold">Wallet Details</span>
                        <span class="text-lg font-bold text-green-700">${{ balance }} (USD)</span>
                        <span class="font-bold text-gray-400">Your Wallet Balance</span>
                    </div>
                </div>
            </div>
        </div>

        <div>
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="mt-8 flow-root">
                    <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                        <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                            <table class="min-w-full divide-y divide-gray-300"
                                v-if="transactions && transactions.length > 0">
                                <thead>
                                    <tr>
                                        <th scope="col"
                                            class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-3">
                                            TID</th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Reference
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">User</th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Order ID
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Debit</th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Credit
                                        </th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Date</th>
                                        <th scope="col"
                                            class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Status
                                        </th>
                                        <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-3">
                                            <span class="sr-only">View</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white">
                                    <tr v-for="transaction in transactions" :key="transaction.id"
                                        class="even:bg-gray-50">
                                        <td
                                            class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-3">
                                            {{ transaction.id }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{
                                            transaction.reference }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{
                                            transaction.user.name }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                                            <Link v-if="transaction.order_id !== 'N/A'"
                                                :href="route('publisher.orders.index')"
                                                class="text-indigo-600 hover:text-indigo-900 flex items-center gap-1">
                                            {{ transaction.order_id }}
                                            </Link>
                                            <span v-else>N/A</span>
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm"
                                            :class="transaction.debit ? 'text-red-600 font-bold' : 'text-gray-500'">
                                            {{ transaction.debit }}
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm"
                                            :class="transaction.credit ? 'text-green-700 font-bold' : 'text-gray-500'">
                                            {{ transaction.credit }}
                                        </td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{
                                            transaction.date }}</td>
                                        <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{
                                            transaction.status }}</td>
                                        <td
                                            class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-3">
                                            <Link
                                                :href="route('publisher.transactions.details', { id: transaction.id })"
                                                class="text-indigo-600 hover:text-indigo-900 flex items-center gap-1">
                                            <Eye class="h-5 w-5" />
                                            <span>View</span>
                                            </Link>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>

                            <div v-else>
                                <NoDataMessage message1="No transaction records found." />

                            </div>
                            <PaginationLinks v-if="transactions.data && transactions.data.length"
                                :links="transactions.links" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { CircleArrowOutUpRight, Eye, Wallet } from 'lucide-vue-next';
import NoDataMessage from '@/Components/NoDataMessage.vue';

const props = defineProps({
    balance: {
        type: String,
        required: true
    },
    transactions: {
        type: Array,
        required: true
    },
});

</script>

<style lang="scss" scoped></style>