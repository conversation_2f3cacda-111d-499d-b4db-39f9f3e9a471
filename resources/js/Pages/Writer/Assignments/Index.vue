<template>
    <div>
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <div class="flex flex-row gap-2 items-center">
                    <h2 class="title-1">

                        {{ query.assignmentType == "completed" ? "Completed Assignments" :
                            query.assignmentType == "assigned" ? "My Assignments" :
                                "All Assignments" }}

                    </h2>
                </div>
                <p class="subtitle-1">My writing assignments.</p>
            </div>
        </div>

        <div class="mt-5">
            <Filters :search="query.search" :assignmentType="query.assignmentType || 'assigned'"
                @update="applyFilters" />
        </div>

        <div class="mt-6 overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-300 bg-white shadow-sm rounded-md">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Actions</th>
                        <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Publisher Website</th>
                        <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Requirements</th>
                        <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Content Status</th>
                        <th class="px-4 py-3 text-left text-sm font-semibold text-gray-700">Assign Date</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                    <tr v-for="item in assignments.data" :key="item.id" class="hover:bg-gray-50">
                        <td class="px-4 py-3 text-sm">
                            <Link :href="route('admin.writer.assignment.show', item.id)" class="btn btn-indigo text-sm">
                            View #{{ item.id }}
                            </Link>
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-700">
                            <div class="flex flex-row gap-2 items-center">
                                {{ item.website?.website_domain ?? 'N/A' }}
                                <a :href="returnWebsiteUrl(item.website)" target="_blank" class="text-indigo-500">
                                    <ExternalLink class="w-4 h-4" />
                                </a>
                            </div>
                            <div class="text-xs text-gray-500 flex flex-col gap-0">
                                <div class="">
                                    <span class="font-bold">Status:</span> <span class="capitalize">{{ item.state_label
                                        }}</span>
                                </div>
                                <div>
                                    <span class="font-bold">Niche:</span> {{ item.niche ?? 'N/A' }},
                                </div>

                            </div>
                        </td>
                        <td class="px-4 py-3  text-gray-700 text-sm">
                            <div>
                                <span class="font-bold">Topic:</span> <span>{{ item.requirements?.article_topic
                                    }}</span>
                            </div>
                            <div class="text-xs text-gray-500">
                                <span class="font-bold">Anchor Text:</span> <span>{{ item.requirements?.anchor_text
                                    }}</span>
                            </div>
                            <div class="text-xs text-gray-500">
                                <span class="font-bold">Advertiser Url:</span> <span>{{
                                    item.requirements?.advertiser_url }}</span>
                            </div>

                        </td>

                        <td class="px-4 py-3 text-sm text-gray-700">
                            <ContentStatus :status="item.content_status" />
                        </td>
                        <td class="px-4 py-3 text-sm text-gray-700">{{ item.created_at_formatted ?? 'N/A' }}</td>

                    </tr>
                </tbody>
            </table>

            <PaginationLinks v-if="assignments.data.length" :links="assignments.links" class="mt-4" />
            <div v-else class="p-4 text-center text-red-500">
                <NoDataMessage message1="No assignments found." message2="Try adjusting your filters." />
            </div>
        </div>
    </div>
</template>

<script setup>
import { Link, router } from '@inertiajs/vue3'
import Filters from './Filters.vue'
import PaginationLinks from '@/Components/PaginationLinks.vue'
import { ExternalLink } from 'lucide-vue-next'
import { returnWebsiteUrl } from '@/lib/utils'
import ContentStatus from '@/Components/ContentStatus.vue'
import NoDataMessage from '@/Components/NoDataMessage.vue';

defineProps({
    assignments: Object,
    statuses: Array,
    query: Object,
});
function applyFilters(newQuery) {
    router.get(route('admin.assignment.index'), newQuery, {
        preserveState: true,
        preserveScroll: true,
    });
}



</script>

<style scoped>
.btn-indigo {
    @apply px-3 py-1 rounded bg-indigo-600 text-white hover:bg-indigo-700;
}
</style>