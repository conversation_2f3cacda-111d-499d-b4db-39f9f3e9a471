<template>
    <div>
        <div class="sm:flex sm:items-center">
            <div class="sm:flex-auto">
                <div class="flex flex-row gap-2 items-center">
                    <h2 class="title-1">My Assignment Stats</h2>
                </div>
                <p class="subtitle-1">Assignment stats.</p>

            </div>
        </div>

        <div class="my-4">
            <DateRangeFilter :preset-range="filters.preset_range" :start-date="filters.start_date"
                :end-date="filters.end_date" @update:filters="onFiltersChange" />
        </div>


        <div>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mt-6">
                <div class="stats-block badge-pending">
                    <div class="stats-block-number">{{ stats.assigned }}</div>
                    <div class="stats-block-label">Assigned</div>
                </div>
                <div class="stats-block badge-pending">
                    <div class="stats-block-number">{{ stats.completed }}</div>
                    <div class="stats-block-label">Completed</div>
                </div>
                <div class="stats-block badge-pending">
                    <div class="stats-block-number">{{ stats.pending }}</div>
                    <div class="stats-block-label">Pending</div>
                </div>

            </div>

        </div>
    </div>
</template>

<script setup>

import DateRangeFilter from '@/Components/DateRangeFilter.vue'
import { router } from '@inertiajs/vue3';

defineProps({
    filters: Object,
    stats: Object,
});

const onFiltersChange = (newFilters) => {
    router.get(route('admin.writer.dashboard'), newFilters, {
        preserveScroll: true,
        preserveState: true,
    });
};


</script>
