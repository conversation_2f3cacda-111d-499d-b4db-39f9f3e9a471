<template>
    <div>
        <div class="flex flex-row gap-2 justify-between items-center">
            <div>
                <h2
                    class="flex justify-start text-2xl text-gray-900 font-bold gap-1 items-center mt-2 sm:text-3xl sm:tracking-tight sm:truncate">
                    <Link :href="route('admin.orders.index')" class="link-indigo-icon">
                    <CircleChevronLeft class="h-6 w-6" />
                    </Link>
                    <span>
                        Orders Details
                    </span>
                </h2>

                <div>
                    <div class="ms-8 mt-4 ">
                        <div class="text-sm">
                            <div class="flex flex-row gap-2 ">
                                <span class="font-bold">Order By:</span>
                                <Link :href="route('admin.users.show', order.user.id)"
                                    class="text-blue-600 hover:underline">
                                <span>{{ order.user?.name }}</span>
                                </Link>
                            </div>
                            <div class="flex flex-row gap-2 ">
                                <span class="font-bold">Advertiser Email:</span>
                                <span>{{ order.user?.email }}</span>
                            </div>
                            <div class="flex flex-row gap-2 ">
                                <span class="font-bold">Time Elapsed:</span>
                                <span>{{ order.time_elapsed }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="hidden">
                <!-- <div class="text-3xl font-bold flex flex-row gap-2 items-center text-green-600 bg-green-100 border border-green-300 px-4 py-2 rounded-lg mr-4"> -->
                <div :class="`status-${order.status} status-large`">
                    <CheckCircle class="h-6 w-6 " />
                    {{ order.status }}
                </div>
            </div>
        </div>


        <div class="flex flex-col gap-y-12 mt-8 border p-4 rounded-lg">
            <!-- create gird with four columns and in each column create a card with the order details -->
            <div class="grid grid-cols-3 gap-0 rounded-lg">
                <div class="overflow-hidden bg-gray-50 shadow py-1">
                    <div class="px-4 py-2 border-b flex flex-row gap-2 text-sm">
                        <span class="font-bold">Order ID:</span>
                        <span>{{ order.id }}</span>
                    </div>
                    <div class="px-4 py-2  flex flex-row gap-2 text-sm">
                        <span class="font-bold">Date Ordered:</span>
                        <span>{{ order.created_at_formatted }}</span>
                    </div>
                </div>
                <div class="overflow-hidden  bg-gray-50 shadow py-1">
                    <div class="px-4 py-2 border-b flex flex-row gap-2 text-sm">
                        <span class="font-bold">Amount Paid:</span>
                        <span>${{ order.payment?.payment_amount }}</span>
                    </div>
                    <div class="px-4 py-2  flex flex-row gap-2 text-sm">
                        <span class="font-bold">Payment Id:</span>
                        <span>{{ order.payment?.id }}</span>
                    </div>

                </div>

                <div class="overflow-hidden  bg-gray-50 shadow py-1">
                    <div class="px-4 py-2 border-b flex flex-row gap-2 text-sm">
                        <span class="font-bold">Publications:</span>
                        <span>0/{{ order.order_items.length }}</span>
                    </div>
                    <div class="px-4 py-2 flex flex-row gap-2 text-sm">
                        <span class="font-bold">Invoice:</span>
                        <Link :href="route('admin.orders.invoice', order.id)" class="link-indigo-icon">
                        <span>View Invoice</span>
                        </Link>
                    </div>

                </div>
            </div>
        </div>

        <div class="flex flex-col gap-y-16 mt-8">
            <Items v-if="pending.length > 0" :items="pending" title="In Progress"></Items>
            <Items v-if="completed.length > 0" :items="completed" title="Completed"></Items>
            <Items v-if="cancelled.length > 0" :items="cancelled" title="Cancelled"></Items>
        </div>
    </div>
</template>

<script setup>
// ==========================================================
// Imports
// ==========================================================
import { CircleChevronLeft, CheckCircle } from 'lucide-vue-next';
import Items from "./Items.vue";

// ==========================================================
// Props
// ==========================================================
defineProps({
    order: Object,
    pending: Object,
    completed: Object,
    cancelled: Object,
});
</script>


<style lang="scss" scoped></style>