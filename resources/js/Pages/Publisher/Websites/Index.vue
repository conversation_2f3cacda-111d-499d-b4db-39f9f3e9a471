<template>
    <div>
        <!-- Header -->
        <div class="sm:flex sm:items-center mb-4">
            <div class="sm:flex-auto">
                <h2 class="title-1">My Websites</h2>
                <p class="subtitle-1">Manage your listed websites and view stats.</p>
                <div class="text-sm text-gray-500">
                    Displaying {{ showingCount }} of {{ websites.total }} websites
                </div>
            </div>
            <Link :href="route('publisher.websites.create')" class="btn-indigo mt-4 sm:mt-0">
            <PlusCircle class="h-5 w-5" />
            <span>Add Website</span>
            </Link>
        </div>

        <!-- Filters -->
        <Filters :searchTerm="filters.searchTerm" :status="filters.status" :perPage="String(filters.perPage)" />

        <!-- Table -->
        <div class="border-b border-gray-200 flow-root mt-6">
            <div class="-mx-4 -my-2 lg:-mx-8 overflow-x-auto sm:-mx-6">
                <div class="align-middle inline-block lg:px-8 min-w-full py-2 sm:px-6">
                    <table v-if="websites.data.length" class="divide-gray-300 divide-y min-w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="text-sm font-semibold text-gray-900 text-left px-4 py-3.5">
                                    <div class="th-content">
                                        <span>Edit</span>
                                    </div>
                                </th>

                                <th @click="sort('id')" class="sortable-th">
                                    <div class="th-content">
                                        <span>
                                            ID
                                        </span>
                                        <div>
                                            <ChevronsUp v-if="filters.sortField === 'id'"
                                                :class="['w-4 h-4 ml-1 transition-transform', filters.sortOrder === 'asc' ? 'rotate-180' : '']" />
                                            <ChevronsUpDown v-else class="w-4 h-4 ml-1" />
                                        </div>
                                    </div>
                                </th>
                                <th @click="sort('website_domain')" class="sortable-th">
                                    <div class="th-content">
                                        <span>
                                            Website
                                        </span>
                                        <div>
                                            <ChevronsUp v-if="filters.sortField === 'website_domain'"
                                                :class="['w-4 h-4 ml-1 transition-transform', filters.sortOrder === 'asc' ? 'rotate-180' : '']" />
                                            <ChevronsUpDown v-else class="w-4 h-4 ml-1" />
                                        </div>
                                    </div>
                                </th>
                                <th @click="sort('orders_count')" class="sortable-th">
                                    <div class="th-content">
                                        <span>
                                            Orders
                                        </span>
                                        <div>
                                            <ChevronsUp v-if="filters.sortField === 'orders_count'"
                                                :class="['w-4 h-4 ml-1 transition-transform', filters.sortOrder === 'asc' ? 'rotate-180' : '']" />
                                            <ChevronsUpDown v-else class="w-4 h-4 ml-1" />
                                        </div>
                                    </div>
                                </th>
                                <th @click="sort('guest_post_price')" class="sortable-th">
                                    <div class="th-content">
                                        <span>
                                            Price
                                        </span>
                                        <div>
                                            <ChevronsUp v-if="filters.sortField === 'guest_post_price'"
                                                :class="['w-4 h-4 ml-1 transition-transform', filters.sortOrder === 'asc' ? 'rotate-180' : '']" />
                                            <ChevronsUpDown v-else class="w-4 h-4 ml-1" />
                                        </div>
                                    </div>
                                </th>
                                <th @click="sort('active')" class="sortable-th">
                                    <div class="th-content">
                                        <span>Status</span>
                                        <div>
                                            <ChevronsUp v-if="filters.sortField === 'active'"
                                                :class="['w-4 h-4 ml-1 transition-transform', filters.sortOrder === 'asc' ? 'rotate-180' : '']" />
                                            <ChevronsUpDown v-else class="w-4 h-4 ml-1" />
                                        </div>
                                    </div>
                                </th>
                                <th class="sortable-th">
                                    <div class="th-content">
                                        <span>Seo Status</span>
                                    </div>
                                </th>
                                <th class="sortable-th">
                                    <div class="th-content">
                                        <span>
                                            Updated
                                        </span>
                                        <div>
                                            <ChevronsUp v-if="filters.sortField === 'updated_at'"
                                                :class="['w-4 h-4 ml-1 transition-transform', filters.sortOrder === 'asc' ? 'rotate-180' : '']" />
                                            <ChevronsUpDown v-else class="w-4 h-4 ml-1" />
                                        </div>
                                    </div>
                                </th>

                            </tr>
                        </thead>

                        <tbody class="divide-y divide-gray-200 bg-white">
                            <tr v-for="website in websites.data" :key="website.id" class="hover:bg-gray-50">
                                <td class="px-4 py-3 text-sm">
                                    <Link :href="route('publisher.websites.edit', website.id)"
                                        class="flex text-indigo-600 gap-1 hover:text-indigo-900 items-center whitespace-nowrap">
                                    <FilePenLine class="size-4" />
                                    <span>Edit</span>
                                    </Link>
                                </td>

                                <td class="px-4 py-3 text-sm text-gray-900">{{ website.id }}</td>
                                <td class="px-4 py-3 text-sm text-gray-900">{{ website.website_domain }}</td>
                                <td class="px-4 py-3 text-sm text-gray-900 text-center">{{ website.orders_count }}</td>
                                <td class="px-4 py-3 text-sm text-gray-600">${{ website.guest_post_price }}</td>
                                <td class="px-4 py-3 text-sm">
                                    <div class="flex items-center gap-2">
                                        <span :class="website.active ? 'text-green-500' : 'text-red-500'">
                                            {{ website.active ? 'Onboarded' : 'Inactive' }}
                                        </span>
                                        <button @click="toggleWebsiteStatus(website)" :disabled="isToggling"
                                            class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                            :class="website.active
                                                ? 'text-red-700 bg-red-100 hover:bg-red-200'
                                                : 'text-green-700 bg-green-100 hover:bg-green-200'">
                                            <Power v-if="!website.active" class="w-3 h-3 mr-1" />
                                            <PowerOff v-if="website.active" class="w-3 h-3 mr-1" />
                                            {{ isToggling ? 'Updating...' : (website.active ? 'Deactivate' : 'Activate')
                                            }}
                                        </button>
                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm">
                                    <div v-if="website.seo_stats_logs_count > 0" class="flex items-center gap-2">
                                        <div class="flex items-center gap-1.5">
                                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                            <span class="text-green-700 font-medium">Available</span>
                                        </div>
                                    </div>
                                    <div v-else class="flex items-center gap-2">
                                        <div class="flex items-center gap-1.5">
                                            <LoaderCircle class="w-4 h-4 animate-spin text-gray-500" />
                                            <span class="text-gray-500 text-sm">Processing...</span>
                                        </div>

                                    </div>
                                </td>
                                <td class="px-4 py-3 text-sm text-gray-600">{{ website.updated_at_formatted }}</td>


                            </tr>
                        </tbody>
                    </table>

                    <div v-else class="p-4 text-center text-red-500">
                        <NoDataMessage message1="No websites found." message2="Try adjusting your filters." />
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <PaginationLinks v-if="websites.data.length" :links="websites.links" :filters="filters" class="mt-4" />

        <!-- Confirmation Dialog -->
        <ConfirmDialog v-model:open="showConfirmDialog" :title="confirmDialogTitle"
            :description="confirmDialogDescription" :cancelText="'Cancel'" :confirmText="confirmDialogConfirmText"
            @confirm="confirmToggle" @cancel="cancelToggle" />
    </div>
</template>

<script setup>
import Filters from './Filters.vue';
import PaginationLinks from '@/Components/PaginationLinks.vue';
import { computed, onMounted, ref } from 'vue';
import { router } from '@inertiajs/vue3';
import { ChevronsUp, ChevronsUpDown, PlusCircle, FilePenLine, Power, PowerOff, LoaderCircle } from 'lucide-vue-next';
import NoDataMessage from '@/Components/NoDataMessage.vue';
import ConfirmDialog from '@/Components/ConfirmDialog.vue';

const props = defineProps({
    websites: Object,
    filters: Object,
});

// Scroll to top when the component is mounted
onMounted(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
})

const isToggling = ref(false);
const showConfirmDialog = ref(false);
const selectedWebsite = ref(null);
const confirmDialogTitle = ref('');
const confirmDialogDescription = ref('');
const confirmDialogConfirmText = ref('');

const sort = (field) => {
    const direction = props.filters.sortField === field && props.filters.sortOrder === 'asc' ? 'desc' : 'asc';
    router.get(route('publisher.websites.index'), {
        searchTerm: props.filters.searchTerm ?? '',
        status: props.filters.status ?? '',
        perPage: props.filters.perPage ?? '10',
        sortField: field,
        sortOrder: direction,
    }, {
        preserveScroll: true,
        preserveState: true,
    });
};

const page = computed(() => {
    const queryPage = new URLSearchParams(window.location.search).get('page');
    return queryPage ? Number(queryPage) : 1;
});

const showingCount = computed(() => {
    const perPage = parseInt(props.filters.perPage || 10);
    const currentPage = page.value;
    const itemsOnPage = props.websites.data.length;
    const totalShown = (currentPage - 1) * perPage + itemsOnPage;
    return totalShown > props.websites.total ? props.websites.total : totalShown;
});

const toggleWebsiteStatus = (website) => {
    selectedWebsite.value = website;
    confirmDialogTitle.value = website.active ? 'Deactivate Website?' : 'Activate Website?';
    confirmDialogDescription.value = website.active
        ? 'Are you sure you want to deactivate this website? It will no longer be available for orders.'
        : 'Are you sure you want to activate this website? It will be available for orders.';
    confirmDialogConfirmText.value = website.active ? 'Yes, Deactivate' : 'Yes, Activate';
    showConfirmDialog.value = true;
};

const confirmToggle = () => {
    if (!selectedWebsite.value || isToggling.value) return;

    isToggling.value = true;
    const website = selectedWebsite.value;
    const newStatus = !website.active;

    router.put(route('publisher.websites.toggle-status', website.id), {
        active: newStatus
    }, {
        preserveScroll: true,
        preserveState: true,
        replace: false,
        onSuccess: () => {
            // Update the local website data
            website.active = newStatus;
            isToggling.value = false;
            selectedWebsite.value = null;
        },
        onError: () => {
            isToggling.value = false;
            selectedWebsite.value = null;
        }
    });
};

const cancelToggle = () => {
    selectedWebsite.value = null;
};
</script>

<style scoped>
.sortable-th {
    @apply text-sm font-semibold text-gray-900 text-left cursor-pointer px-4 py-3.5;
}

.th-content {
    @apply flex flex-row items-center whitespace-nowrap;
}
</style>